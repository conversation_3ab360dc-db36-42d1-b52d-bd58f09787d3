import datetime
from dataclasses import dataclass, field
from typing import List

from entity.enum.trade_mode import TradeMode
from entity.param_instance import ParamInstance


@dataclass
class Param:
    code: str
    # fixed, load from config
    cat: str
    account_name: str
    contract_code: str
    # time range，单位秒
    delta_time: int
    # 符号都是正的，比如 0.02 代表 2%
    delta_percent: float
    # 符号都是正的
    # delta_percent * alpha to close
    alpha: float
    trade_mode: TradeMode

    # stateful
    next_open_time: datetime.datetime = None
    instance_list: List[ParamInstance] = field(default_factory=list)

    def __post_init__(self):
        if self.delta_time <= 0:
            raise ValueError(f"delta_time must be greater than 0, got {self.delta_time}")

        if self.delta_percent <= 0:
            raise ValueError(f"delta_percent must be greater than 0, got {self.delta_percent}")

        if self.alpha <= 0:
            raise ValueError(f"alpha must be greater than 0, got {self.alpha}")
