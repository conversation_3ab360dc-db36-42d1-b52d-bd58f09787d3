import unittest

import pandas as pd

from TqMockApi import TqMockApi
from entity.enum.trade_mode import TradeMode
from entity.param import Param
from entity.segment_mock_data import SegmentMockData
from entity.trade_input import TradeInput
from new_trade.mock_trade_executor import MockTradeExecutor
from new_trade.visualize import draw_line
from risk.run_risk import run_risk
from unit_test.base import TEST_DATA_ROOT_DIR_IGNORED
from util.file_util import rmdir_if_exists, mkdir_if_not_exists
from util.trade_util import generate_mock_data, get_instance_list


class TestTradeByMockComplexData(unittest.TestCase):

    def setUp(self):
        """在每个测试方法执行前清空测试数据目录"""
        # 清空测试数据目录
        rmdir_if_exists(TEST_DATA_ROOT_DIR_IGNORED)
        # 重新创建测试数据目录
        mkdir_if_not_exists(TEST_DATA_ROOT_DIR_IGNORED)

    def test1(self):
        param = Param(
            code="same_trend_stop_profit",
            account_name="fx",
            cat="cat",
            contract_code="normal_before_eod",
            delta_time=120,
            # 3%涨幅触发
            delta_percent=0.03,
            alpha=1,
            trade_mode=TradeMode.SAME_TREND_STOP_PROFIT
        )
        volatility = 0.5
        segment_mock_data_list = [
            # 1 小时内没有涨太多，不开仓
            SegmentMockData("2025-06-06 09:00:00", "2025-06-06 10:00:00", 1000, 1010),
            # 模拟 2 分钟内上涨 3% 触发开仓
            SegmentMockData("2025-06-06 10:00:01", "2025-06-06 10:02:00", 1010, 1040),
            SegmentMockData("2025-06-06 10:02:01", "2025-06-06 14:00:00", 1010, 1040)
        ]
        trade_input = TradeInput(param_list=[param],
                                 code=param.contract_code,
                                 init_fund=********,
                                 backtest_start_time=segment_mock_data_list[0].start_time_str,
                                 backtest_end_time=segment_mock_data_list[-1].end_time_str,
                                 cd_ratio=1.0)
        total_time_list, total_price_list = generate_mock_data(segment_mock_data_list, volatility)
        start_time = pd.to_datetime(trade_input.backtest_start_time)
        end_time = pd.to_datetime(trade_input.backtest_end_time)
        # ToDo(hm): from here 运行还是有点慢，看下还能优化吗？
        api = TqMockApi(start_dt=start_time, end_dt=end_time, time_list=total_time_list, price_list=total_price_list)
        trade_executor = MockTradeExecutor(TEST_DATA_ROOT_DIR_IGNORED, 100)
        trade_result = run_risk(trade_input, api, trade_executor)
        all_instances = get_instance_list(trade_result.param_list)
        draw_line(total_price_list, all_instances, total_time_list)


if __name__ == '__main__':
    unittest.main()
